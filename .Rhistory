load("/Users/<USER>/Desktop/rectal_cancer_MRI/rectal_cancer_data_cleaned.Rdata")
View(new_data)
View(new_data)
summary(show_new_seer)
show_new_seer <- new_data[show_vars1]
show_new_seer <- new_data[show_vars1]
library(moonBook)
str(show_new_seer)
rm(list = ls())
setwd('/Users/<USER>/Desktop/rectal_cancer_MRI')
load("rectal_cancer_data_cleaned.Rdata")
str(new_data)
colnames(new_data)
######moonbook包制作临床三线图######
show_vars1 <- c("Age","gender.1-male.2-female.","Tumor_Size","Height.m.","tumor.differentiation",
"CEA（ng/mL）","CA-199.U/mL.","CA-242.U/mL.","肿瘤T分期.29",
"肿瘤N分期.30","侧方淋巴结.31","分期.32","腹膜返折","肿瘤距肛门距离.cm.",
"EMVI.1有，2无.35","环周切缘.CRM，1阳性，2阴性）","肛提肌侵犯，1侵犯，2无",
"肿瘤长径（MRI）.38","肿瘤厚度","肠道占比.40","GTV.㎤.","Radiotherapy.Dose",
,"Concurrent.chemotherapy","Adjuvant.chemotherapy","AC-times","CCR.1-yes.0-no.",
,"Efficacy.evaluation","肿瘤T分期.55","肿瘤N分期.56","侧方淋巴结.57","EMVI.1有，2无.60",
"环周切缘.CRM）","Sur-T","Sur-N","AJCC","TRG（AJCC）","pCR（1-yes，0-no）",
"Postoperative.chemotherapy.times","Postoperative.chemotherapy","Local.recurrence","Local.recurrence.time",
"Metastasis.location","生存状态（1=复发转移；2=未复发转移）","PFS-time","LPFS-status","LPFS-time",
"DMFS-status","DMFS-time","生存状态（1=死亡；2=生存）","OS-time","cCR/ncCR")#包括自变量和协变量
######moonbook包制作临床三线图######
show_vars1 <- c("Age","gender.1-male.2-female.","Tumor_Size","Height.m.","tumor.differentiation",
"CEA（ng/mL）","CA-199.U/mL.","CA-242.U/mL.","肿瘤T分期.29",
"肿瘤N分期.30","侧方淋巴结.31","分期.32","腹膜返折","肿瘤距肛门距离.cm.",
"EMVI.1有，2无.35","环周切缘.CRM，1阳性，2阴性）","肛提肌侵犯，1侵犯，2无",
"肿瘤长径（MRI）.38","肿瘤厚度","肠道占比.40","GTV.㎤.","Radiotherapy.Dose",
,"Concurrent.chemotherapy","Adjuvant.chemotherapy","AC-times","CCR.1-yes.0-no.",
,"Efficacy.evaluation","肿瘤T分期.55","肿瘤N分期.56","侧方淋巴结.57","EMVI.1有，2无.60",
"环周切缘.CRM）","Sur-T","Sur-N","AJCC","TRG（AJCC）","pCR（1-yes，0-no）",
"Postoperative.chemotherapy.times","Postoperative.chemotherapy","Local.recurrence","Local.recurrence.time",
"Metastasis.location","生存状态（1=复发转移；2=未复发转移）","PFS-time","LPFS-status","LPFS-time",
"DMFS-status","DMFS-time","生存状态（1=死亡；2=生存）","OS-time","cCR/ncCR")#包括自变量和协变量
######moonbook包制作临床三线图######
show_vars1 <- c("Age","gender.1-male.2-female.","Tumor_Size","Height.m.","tumor.differentiation",
"CEA（ng/mL）","CA-199.U/mL.","CA-242.U/mL.","肿瘤T分期.29",
"肿瘤N分期.30","侧方淋巴结.31","分期.32","腹膜返折","肿瘤距肛门距离.cm.",
"EMVI.1有，2无.35","环周切缘.CRM，1阳性，2阴性）","肛提肌侵犯，1侵犯，2无",
"肿瘤长径（MRI）.38","肿瘤厚度","肠道占比.40","GTV.㎤.","Radiotherapy.Dose",
"Concurrent.chemotherapy","Adjuvant.chemotherapy","AC-times","CCR.1-yes.0-no.",
"Efficacy.evaluation","肿瘤T分期.55","肿瘤N分期.56","侧方淋巴结.57","EMVI.1有，2无.60",
"环周切缘.CRM）","Sur-T","Sur-N","AJCC","TRG（AJCC）","pCR（1-yes，0-no）",
"Postoperative.chemotherapy.times","Postoperative.chemotherapy","Local.recurrence","Local.recurrence.time",
"Metastasis.location","生存状态（1=复发转移；2=未复发转移）","PFS-time","LPFS-status","LPFS-time",
"DMFS-status","DMFS-time","生存状态（1=死亡；2=生存）","OS-time","cCR/ncCR")#包括自变量和协变量
#strata_vars <- c("Chemotherapy")
show_new_seer <- new_data[show_vars1]
#strata_vars <- c("Chemotherapy")
show_new_seer <- new_data[show_vars1]
