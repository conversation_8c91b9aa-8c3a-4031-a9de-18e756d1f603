rm(list = ls())
setwd('/Users/<USER>/Desktop/rectal_cancer_MRI') # 请修改为您的工作目录
library(readxl)
library(dplyr)
library(writexl) # 添加writexl包用于输出xlsx文件

# 读取数据
file_path <- "CR-nearCR1.xlsx"  # 请替换为您的Excel文件路径
sheet_name <- "Sheet1"  # 请替换为您的工作表名称
data <- read_excel(file_path, sheet = sheet_name)

# 查看列名和数据结构
print("原始列名:")
print(colnames(data))
print("数据结构:")
str(data)

# 将列名中的空格和特殊字符替换为点
colnames(data) <- gsub("[ ,()]", ".", colnames(data))
colnames(data) <- gsub("\\.\\.+", ".", colnames(data))

# 查看处理后的列名
print("处理后的列名:")
print(colnames(data))

# 重命名关键变量 - 根据实际列名动态调整
if("patient´ID.number" %in% colnames(data)) names(data)[names(data) == "patient´ID.number"] <- "Patient.ID"
if("gender.1.male.2.female." %in% colnames(data)) names(data)[names(data) == "gender.1.male.2.female."] <- "Sex"
if("age" %in% colnames(data)) names(data)[names(data) == "age"] <- "Age"
if("生存状态.1.复发转移.2.未复发转移." %in% colnames(data)) names(data)[names(data) == "生存状态.1.复发转移.2.未复发转移."] <- "Recurrence_status"
if("生存状态.1.死亡.2.生存." %in% colnames(data)) names(data)[names(data) == "生存状态.1.死亡.2.生存."] <- "Vital_status"

# 查找可能的生存时间变量
surv_time_cols <- grep("生存时间|随访时间|OS|survival|follow.*up|time", colnames(data), ignore.case = TRUE, value = TRUE)
print("可能的生存时间列:")
print(surv_time_cols)

# 如果找到了可能的生存时间列，使用第一个
if(length(surv_time_cols) > 0) {
  surv_time_var <- surv_time_cols[1]
  data$Survival.months <- data[[surv_time_var]]
  print(paste("使用", surv_time_var, "作为生存时间变量"))
} else {
  # 如果没有找到，尝试手动指定
  print("未找到生存时间变量，请检查数据或手动指定")
}

# 处理性别变量
if("Sex" %in% colnames(data)) {
  data$Sex <- ifelse(!is.na(data$Sex) & data$Sex == 1, "Male", 
                    ifelse(!is.na(data$Sex) & data$Sex == 2, "Female", NA))
} else if("gender.1.male.2.female." %in% colnames(data)) {
  data$Sex <- ifelse(!is.na(data$gender.1.male.2.female.) & data$gender.1.male.2.female. == 1, "Male", 
                    ifelse(!is.na(data$gender.1.male.2.female.) & data$gender.1.male.2.female. == 2, "Female", NA))
}

# 处理肿瘤大小
data$Tumor_Size <- NA
# 检查肿瘤长径列名
tumor_size_cols <- grep("肿瘤长径", colnames(data), value = TRUE)
if(length(tumor_size_cols) > 0) {
  tumor_size_col <- tumor_size_cols[1]  # 使用第一个匹配的列
  print(paste("使用肿瘤长径列:", tumor_size_col))
  
  for(i in 1:nrow(data)){
    if(!is.na(data[[tumor_size_col]][i])){
      size_value = as.numeric(data[[tumor_size_col]][i])
      if(size_value <= 20){
        data$Tumor_Size[i] = "001-020"
      } else if(size_value <= 30){
        data$Tumor_Size[i] = "021-030"
      } else if(size_value <= 40){
        data$Tumor_Size[i] = "031-040"
      } else if(size_value <= 50){
        data$Tumor_Size[i] = "041-050"
      } else if(size_value <= 60){
        data$Tumor_Size[i] = "051-060"
      } else {
        data$Tumor_Size[i] = "060+"
      }
    } else {
      data$Tumor_Size[i] = "Unknown"
    }
  }
}
table(data$Tumor_Size)

# 处理治疗方式
# 放疗状态
if("Radiotherapy.Dose" %in% colnames(data)) {
  data$Radiotherapy <- ifelse(!is.na(data$Radiotherapy.Dose) & data$Radiotherapy.Dose != "", "Yes", "No")
} else {
  data$Radiotherapy <- "Unknown"
}

# 计算放疗持续时间
if("Time-end.radiotherapy" %in% colnames(data) && "Time-start.radiotherapy" %in% colnames(data)) {
  data$`radiotherapy-during-time` <- data$`Time-end.radiotherapy` - data$`Time-start.radiotherapy`
  # 处理可能的负值或异常值
  data$`radiotherapy-during-time`[data$`radiotherapy-during-time` < 0] <- NA
  print("已添加放疗持续时间列: radiotherapy-during-time")
  print(summary(data$`radiotherapy-during-time`))
} else {
  print("未找到放疗开始或结束时间列，无法计算放疗持续时间")
}

# 计算放疗结束到手术的时间间隔
if("Surgery.time" %in% colnames(data) && "Time-end.radiotherapy" %in% colnames(data)) {
  data$`post-radio-pre-surg` <- data$`Surgery.time` - data$`Time-end.radiotherapy`
  # 处理可能的负值或异常值
  data$`post-radio-pre-surg`[data$`post-radio-pre-surg` < 0] <- NA
  print("已添加放疗结束到手术时间间隔列: post-radio-pre-surg")
  print(summary(data$`post-radio-pre-surg`))
} else {
  print("未找到手术时间或放疗结束时间列，无法计算放疗结束到手术的时间间隔")
}

# 化疗状态
if("Concurrent.chemotherapy" %in% colnames(data)) {
  data$Chemotherapy <- ifelse(!is.na(data$Concurrent.chemotherapy) & data$Concurrent.chemotherapy != "无", "Yes", "No")
} else {
  data$Chemotherapy <- "Unknown"
}

# 手术状态
if("Surgery.0.no.1.yes." %in% colnames(data)) {
  data$Surgery <- ifelse(data$Surgery.0.no.1.yes. == "1", "Yes", "No")
} else if("Surgery" %in% colnames(data)) {
  # 如果已有Surgery列，保留原值
} else {
  # 尝试从其他列推断手术状态
  if("Surgery.time" %in% colnames(data)) {
    data$Surgery <- ifelse(!is.na(data$Surgery.time), "Yes", "No")
  } else {
    data$Surgery <- "Unknown"
  }
}

# 综合治疗方式
data$Treatment <- NA
for(i in 1:nrow(data)){
  if(data$Surgery[i] == "Yes" && data$Radiotherapy[i] == "Yes" && data$Chemotherapy[i] == "Yes"){
    data$Treatment[i] = "Surgery_RT_Chemo"
  } else if(data$Surgery[i] == "Yes" && data$Radiotherapy[i] == "Yes"){
    data$Treatment[i] = "Surgery_RT"
  } else if(data$Surgery[i] == "Yes" && data$Chemotherapy[i] == "Yes"){
    data$Treatment[i] = "Surgery_Chemo"
  } else if(data$Surgery[i] == "Yes"){
    data$Treatment[i] = "Surgery_alone"
  } else if(data$Radiotherapy[i] == "Yes" && data$Chemotherapy[i] == "Yes"){
    data$Treatment[i] = "RT_Chemo"
  } else {
    data$Treatment[i] = "Other"
  }
}
table(data$Treatment)

# 处理生存状态
# 确认生存时间变量
surv_time_var <- NULL
if("Survival.months" %in% colnames(data) && !all(is.na(data$Survival.months))) {
  surv_time_var <- "Survival.months"
} else if("OS.time" %in% colnames(data) && !all(is.na(data$OS.time))) {
  surv_time_var <- "OS.time"
}

# 只有在存在生存时间变量的情况下才处理生存状态
if(!is.null(surv_time_var) && length(data[[surv_time_var]]) > 0 && !all(is.na(data[[surv_time_var]]))) {
  print(paste("使用", surv_time_var, "计算生存状态"))
  
  # 1年生存状态
  data$One_year_status <- ifelse(data[[surv_time_var]] < 12, 1, 0)
  if("Vital_status" %in% colnames(data)) {
    data$One_year_status_data <- ifelse(data[[surv_time_var]] < 12 & data$Vital_status == 1, 1, 0)
  }
  
  # 3年生存状态
  data$Three_year_status <- ifelse(data[[surv_time_var]] < 36, 1, 0)
  if("Vital_status" %in% colnames(data)) {
    data$Three_year_status_data <- ifelse(data[[surv_time_var]] < 36 & data$Vital_status == 1, 1, 0)
  }
  
  # 5年生存状态
  data$Five_year_status <- ifelse(data[[surv_time_var]] < 60, 1, 0)
  if("Vital_status" %in% colnames(data)) {
    data$Five_year_status_data <- ifelse(data[[surv_time_var]] < 60 & data$Vital_status == 1, 1, 0)
  }
} else {
  # 如果没有生存时间变量，设置默认值
  data$One_year_status <- NA
  data$One_year_status_data <- NA
  data$Three_year_status <- NA
  data$Three_year_status_data <- NA
  data$Five_year_status <- NA
  data$Five_year_status_data <- NA
  warning("未找到生存时间变量，无法计算生存状态")
}

# 动态构建可用变量列表
# 首先保留所有原始变量
original_vars <- colnames(data)

# 排除明显无用的变量（如全NA或常量）
exclude_vars <- c()
for(var in original_vars) {
  # 检查变量是否全为NA
  if(all(is.na(data[[var]]))) {
    exclude_vars <- c(exclude_vars, var)
    print(paste("变量", var, "全为NA，将被排除"))
    next
  }
  
  # 检查变量是否为常量（只有一个唯一值）
  if(length(unique(data[[var]])) == 1) {
    exclude_vars <- c(exclude_vars, var)
    print(paste("变量", var, "为常量，将被排除"))
    next
  }
}

# 获取所有有用的变量
useful_vars <- setdiff(original_vars, exclude_vars)
print("有用变量数量:")
print(length(useful_vars))

# 确保关键变量被包含
key_vars <- c("Patient.ID", "Age", "Sex", "Tumor_Size", "Radiotherapy", 
              "Chemotherapy", "Surgery", "Treatment", "Survival.months", 
              "Vital_status", "One_year_status", "Three_year_status", "Five_year_status")
key_vars <- key_vars[key_vars %in% colnames(data)]

# 合并所有要保留的变量
all_vars_to_keep <- unique(c(key_vars, useful_vars))
print("最终保留变量数量:")
print(length(all_vars_to_keep))
print("最终保留变量:")
print(all_vars_to_keep)

# 筛选变量
new_data <- data[all_vars_to_keep]

# 确定哪些变量应该是分类变量
# 检查每个变量的类型和唯一值数量
var_info <- data.frame(
  Variable = character(),
  Type = character(),
  UniqueValues = numeric(),
  stringsAsFactors = FALSE
)

for(var in colnames(new_data)) {
  var_type <- class(new_data[[var]])
  unique_values <- length(unique(na.omit(new_data[[var]])))
  var_info <- rbind(var_info, data.frame(
    Variable = var,
    Type = var_type[1],
    UniqueValues = unique_values,
    stringsAsFactors = FALSE
  ))
}

print("变量信息:")
print(var_info)

# 自动识别分类变量
# 1. 字符型变量
# 2. 唯一值少于10的数值变量
cat_vars <- c()
for(i in 1:nrow(var_info)) {
  var <- var_info$Variable[i]
  if(var_info$Type[i] == "character" || 
     (var_info$Type[i] %in% c("numeric", "integer") && var_info$UniqueValues[i] < 10)) {
    cat_vars <- c(cat_vars, var)
  }
}

# 手动添加已知的分类变量
additional_cat_vars <- c("Sex", "Tumor_Size", "Radiotherapy", "Chemotherapy", 
                         "Surgery", "Treatment", "Vital_status", 
                         "One_year_status", "Three_year_status", "Five_year_status")
cat_vars <- unique(c(cat_vars, additional_cat_vars[additional_cat_vars %in% colnames(new_data)]))

print("识别的分类变量:")
print(cat_vars)

# 将分类变量转换为因子
for(var in cat_vars) {
  if(var %in% colnames(new_data)) {
    new_data[[var]] <- as.factor(new_data[[var]])
  }
}

# 保存数据框为RData格式
save(new_data, file = "rectal_cancer_MRI_cleaned.Rdata")

# 保存数据框为Excel格式
# 首先将因子转换为字符串，以便在Excel中正确显示
new_data_excel <- new_data
for(var in cat_vars) {
  if(var %in% colnames(new_data_excel)) {
    new_data_excel[[var]] <- as.character(new_data_excel[[var]])
  }
}
write_xlsx(new_data_excel, "rectal_cancer_MRI_cleaned.xlsx")
print("数据已保存为Excel格式：rectal_cancer_MRI_cleaned.xlsx")

# 查看因子水平
lever <- function(x) {
  if(is.factor(x)) {
    return(levels(x))
  } else {
    return("Not a factor")
  }
}
factor_levels <- lapply(new_data[cat_vars[cat_vars %in% colnames(new_data)]], lever)
print("因子水平:")
print(factor_levels)

# 显示变量表格
tables_list <- lapply(cat_vars[cat_vars %in% colnames(new_data)], function(var) {
  table(new_data[[var]], useNA = "ifany")
})
names(tables_list) <- cat_vars[cat_vars %in% colnames(new_data)]
print("分类变量频数表:")
print(tables_list)

# 保存最终数据框为RData格式
str(new_data)
save(new_data, file = "rectal_cancer_data_cleaned.Rdata")

# 保存最终数据框为Excel格式
write_xlsx(new_data_excel, "rectal_cancer_data_cleaned.xlsx")
print("最终数据已保存为Excel格式：rectal_cancer_data_cleaned.xlsx")
 
